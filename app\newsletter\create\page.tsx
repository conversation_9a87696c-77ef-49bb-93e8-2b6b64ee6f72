"use client"

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Spinner } from '@/components/ui/shadcn-io/spinner'
import { Table, TableBody, TableHeader } from '@/components/ui/table'
import { useBrands, useLanguages } from '@/hooks'
import { useTemplates } from '@/hooks/use-templates'
import { NextPage } from 'next'
import { useEffect, useState } from 'react'

interface Props { }

const Page: NextPage<Props> = ({ }) => {
  const { brands, loading: brandsLoading } = useBrands()
  const { templates, loading: templatesLoading } = useTemplates()
  const { languages, loading: languagesLoading } = useLanguages()

  const [currentStep, setCurrentStep] = useState<number>(1)
  const [canMoveNext, setCanMoveNext] = useState<boolean>(false)
  const [canMoveBack, setCanMoveBack] = useState<boolean>(false)

  const [brand, setBrand] = useState<string>("")
  const [formData, setFormData] = useState<{
    name: string
    template: string
    languages: string[]
  }>({
    name: '',
    template: '',
    languages: [''
  })

  useEffect(() => {
    if (currentStep === 1) {
      setCanMoveBack(false)
      setCanMoveNext(brand !== "")
    } else {
      setCanMoveBack(true)
    }
  }, [currentStep])

  const SelectBrand = () => {

    if (brandsLoading) return <Spinner key="infinite" variant="infinite" size={64} />


    return (
      <>
        <div className='space-y-2'>
          <div className='text-lg font-medium'>Selecciona la marca</div>
          <div className='space-x-2'>
            {
              brands.map(brand => (
                <Button key={brand.id} onClick={() => { setBrand(brand.id); setCurrentStep(2) }}>
                  {brand.name}
                </Button>
              ))
            }
          </div>
        </div>
      </>
    )
  }

  const SelectTemplate = () => {
    if (templatesLoading) return <Spinner key="infinite" variant="infinite" size={64} />

    return (
      <>
        <div className='space-y-2'>
          <div className='text-lg font-medium'>Selecciona la plantilla</div>
          <div className='space-x-2'>
            <Table>
              <TableHeader>
                <tr>
                  <th>Nom</th>
                  <th>Descripció</th>
                </tr>
              </TableHeader>
              <TableBody>
                {templates.map((template) => (
                  <tr key={template.id} className="cursor-pointer hover:bg-muted" onClick={() => { setFormData({ ...formData, template: template.id }); setCurrentStep(3) }}>
                    <td>{template.name}</td>
                    <td>{template.description}</td>
                  </tr>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>
      </>

    )
  }

  const SelectLanguagesAndNewsletterName = () => {
    if (languagesLoading) return <Spinner key="infinite" variant="infinite" size={64} />

    return (
      <>
        <div className='space-y-2'>
          <div className='text-lg font-medium'>Selecciona els idiomes</div>
          <div className='space-x-2'>
            {
              languages.map(language => (
                <>
                                <Checkbox
                  key={language.language}
                  checked={formData.languages.includes(language.language)}
                  defaultChecked={formData.languages.includes(language.language)}
                  onCheckedChange={(checked) => {
                    setFormData(prev => ({
                      ...prev,
                      languages: checked ? [...prev.languages, language.language] : prev.languages.filter(l => l !== language.language)
                    }))
                  }}
                />
                <label>{language.language_display} ({language.language})</label>
                </>
              ))
            }
          </div>
        </div>
      </>
    )
  }

  return (
    <div className="p-6 flex justify-center items-start min-h-screen">
      <div className="space-y-4 flex flex-col w-full max-w-4xl">

        <Card>
          <CardHeader className='pb-2 pt-0 mt-3'>
            <div className="flex items-center justify-between flex-row">
              <div>
                <h1 className="text-3xl font-bold tracking-tight">Crear Newsletter</h1>
                <p className="text-muted-foreground">Crea una nova newsletter</p>
              </div>
              <div className='text-right text-sm text-muted-foreground'>
                <div>Pas {currentStep} de 3</div>
                <div><progress value={currentStep} max={3} /></div>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div>
              selected info
              <div>Brand: {brand}</div>
              <div>Template: {formData.template}</div>
              <div>Languages: {formData.languages.join(", ")}</div>
              </div>
            {
              currentStep === 1 ? (<SelectBrand />)
                : currentStep === 2 ? (<SelectTemplate />)
                  : (<SelectLanguagesAndNewsletterName />)
            }

            <div className='mt-4 flex justify-between'>
              {
                currentStep !== 1 ? (
                  <Button disabled={!canMoveBack} onClick={() => setCurrentStep(currentStep - 1)}>Enrere</Button>
                ) : <div></div>
              }
              <Button disabled={!canMoveNext} onClick={() => setCurrentStep(currentStep + 1)}>Següent</Button>
            </div>
          </CardContent>
        </Card>

      </div>
    </div>
  )
}

export default Page