"use client"

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Spinner } from '@/components/ui/shadcn-io/spinner'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Badge } from '@/components/ui/badge'

import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator
} from '@/components/ui/breadcrumb'
import { useBrands, useLanguages } from '@/hooks'
import { useTemplates } from '@/hooks/use-templates'
import { NextPage } from 'next'
import { useEffect, useState } from 'react'
import { Check, ChevronRight, Building2, FileText, Languages } from 'lucide-react'

interface Props { }

const Page: NextPage<Props> = ({ }) => {
  const { brands, loading: brandsLoading } = useBrands()
  const { templates, loading: templatesLoading } = useTemplates()
  const { languages, loading: languagesLoading } = useLanguages()

  const [currentStep, setCurrentStep] = useState<number>(1)
  const [canMoveNext, setCanMoveNext] = useState<boolean>(false)
  const [canMoveBack, setCanMoveBack] = useState<boolean>(false)

  const [brand, setBrand] = useState<string>("")
  const [formData, setFormData] = useState<{
    name: string
    template: string
    languages: string[]
  }>({
    name: '',
    template: '',
    languages: []
  })

  useEffect(() => {
    if (currentStep === 1) {
      setCanMoveBack(false)
      setCanMoveNext(brand !== "")
    } else if (currentStep === 2) {
      setCanMoveBack(true)
      setCanMoveNext(formData.template !== "")
    } else if (currentStep === 3) {
      setCanMoveBack(true)
      setCanMoveNext(formData.name.trim() !== "" && formData.languages.length > 0)
    }
  }, [currentStep, brand, formData.template, formData.name, formData.languages])

  const steps = [
    { id: 1, name: 'Marca', description: 'Selecciona la marca', icon: Building2 },
    { id: 2, name: 'Plantilla', description: 'Escull una plantilla', icon: FileText },
    { id: 3, name: 'Configuració', description: 'Nom i idiomes', icon: Languages },
  ]

  const getSelectedBrand = () => brands.find(b => b.id === brand)
  const getSelectedTemplate = () => templates.find(t => t.id === formData.template)

  const SelectBrand = () => {
    if (brandsLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <Spinner key="infinite" variant="infinite" size={64} />
        </div>
      )
    }

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold">Selecciona la marca</h3>
          <p className="text-sm text-muted-foreground">
            Escull la marca per a la qual vols crear la newsletter
          </p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {brands.map((brandOption) => (
            <Card
              key={brandOption.id}
              className={`cursor-pointer transition-all hover:shadow-md ${
                brand === brandOption.id ? 'ring-2 ring-primary bg-primary/5' : 'hover:bg-muted/50'
              }`}
              onClick={() => setBrand(brandOption.id)}
            >
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <h4 className="font-medium">{brandOption.name}</h4>
                    <p className="text-sm text-muted-foreground mt-1">
                      Marca corporativa
                    </p>
                  </div>
                  {brand === brandOption.id && (
                    <Check className="h-5 w-5 text-primary" />
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  const SelectTemplate = () => {
    if (templatesLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <Spinner key="infinite" variant="infinite" size={64} />
        </div>
      )
    }

    // Filter templates by selected brand
    const filteredTemplates = templates.filter(template => template.brand === brand)

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold">Selecciona la plantilla</h3>
          <p className="text-sm text-muted-foreground">
            Escull una plantilla per a la marca {getSelectedBrand()?.name}
          </p>
        </div>

        {filteredTemplates.length === 0 ? (
          <Card>
            <CardContent className="p-6 text-center">
              <p className="text-muted-foreground">
                No hi ha plantilles disponibles per aquesta marca.
              </p>
            </CardContent>
          </Card>
        ) : (
          <div className="overflow-hidden rounded-lg border">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Nom</TableHead>
                  <TableHead>Descripció</TableHead>
                  <TableHead className="w-[100px]">Estat</TableHead>
                  <TableHead className="w-[80px]"></TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredTemplates.map((template) => (
                  <TableRow
                    key={template.id}
                    className={`cursor-pointer transition-colors ${
                      formData.template === template.id ? 'bg-primary/5' : 'hover:bg-muted/50'
                    }`}
                    onClick={() => setFormData({ ...formData, template: template.id })}
                  >
                    <TableCell className="font-medium">{template.name}</TableCell>
                    <TableCell className="text-muted-foreground">
                      {template.description || 'Sense descripció'}
                    </TableCell>
                    <TableCell>
                      <Badge variant={template.is_active ? "default" : "secondary"}>
                        {template.is_active ? "Activa" : "Inactiva"}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {formData.template === template.id && (
                        <Check className="h-4 w-4 text-primary" />
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </div>
    )
  }

  const SelectLanguagesAndNewsletterName = () => {
    if (languagesLoading) {
      return (
        <div className="flex items-center justify-center py-12">
          <Spinner key="infinite" variant="infinite" size={64} />
        </div>
      )
    }

    return (
      <div className="space-y-6">
        <div>
          <h3 className="text-lg font-semibold">Configuració final</h3>
          <p className="text-sm text-muted-foreground">
            Defineix el nom de la newsletter i selecciona els idiomes
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Newsletter Name */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Nom de la Newsletter</CardTitle>
              <CardDescription>
                Introdueix un nom descriptiu per a aquesta newsletter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                <Label htmlFor="newsletter-name">Nom *</Label>
                <Input
                  id="newsletter-name"
                  placeholder="Ex: Newsletter Setmanal Gener 2024"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({
                    ...prev,
                    name: e.target.value.substring(0, 100)
                  }))}
                  maxLength={100}
                />
                <p className="text-xs text-muted-foreground">
                  {formData.name.length}/100 caràcters
                </p>
              </div>
            </CardContent>
          </Card>

          {/* Languages Selection */}
          <Card>
            <CardHeader>
              <CardTitle className="text-base">Idiomes</CardTitle>
              <CardDescription>
                Selecciona els idiomes en què es publicarà la newsletter
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {languages.map((language) => (
                  <div key={language.language} className="flex items-center space-x-3">
                    <Checkbox
                      id={`lang-${language.language}`}
                      checked={formData.languages.includes(language.language)}
                      onCheckedChange={(checked) => {
                        setFormData(prev => ({
                          ...prev,
                          languages: checked
                            ? [...prev.languages, language.language]
                            : prev.languages.filter(l => l !== language.language)
                        }))
                      }}
                    />
                    <Label
                      htmlFor={`lang-${language.language}`}
                      className="text-sm font-normal cursor-pointer"
                    >
                      {language.language_display} ({language.language.toUpperCase()})
                    </Label>
                  </div>
                ))}
                {formData.languages.length === 0 && (
                  <p className="text-xs text-muted-foreground">
                    Selecciona almenys un idioma
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      <div className="container mx-auto px-4 py-8 max-w-6xl">
        {/* Header */}
        <div className="mb-8">
          <Breadcrumb>
            <BreadcrumbList>
              <BreadcrumbItem>
                <BreadcrumbLink href="/newsletter">Newsletters</BreadcrumbLink>
              </BreadcrumbItem>
              <BreadcrumbSeparator />
              <BreadcrumbItem>
                <BreadcrumbPage>Crear Newsletter</BreadcrumbPage>
              </BreadcrumbItem>
            </BreadcrumbList>
          </Breadcrumb>

          <div className="mt-4">
            <h1 className="text-3xl font-bold tracking-tight">Crear Newsletter</h1>
            <p className="text-muted-foreground mt-2">
              Segueix els passos per crear una nova newsletter
            </p>
          </div>
        </div>

        {/* Step Indicator */}
        <Card className="mb-8">
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              {steps.map((step, index) => {
                const StepIcon = step.icon
                const isActive = currentStep === step.id
                const isCompleted = currentStep > step.id

                return (
                  <div key={step.id} className="flex items-center">
                    <div className="flex items-center">
                      <div className={`
                        flex items-center justify-center w-10 h-10 rounded-full border-2 transition-colors
                        ${isCompleted
                          ? 'bg-primary border-primary text-primary-foreground'
                          : isActive
                            ? 'border-primary text-primary bg-primary/10'
                            : 'border-muted-foreground/30 text-muted-foreground'
                        }
                      `}>
                        {isCompleted ? (
                          <Check className="w-5 h-5" />
                        ) : (
                          <StepIcon className="w-5 h-5" />
                        )}
                      </div>
                      <div className="ml-3">
                        <p className={`text-sm font-medium ${
                          isActive ? 'text-foreground' : 'text-muted-foreground'
                        }`}>
                          {step.name}
                        </p>
                        <p className="text-xs text-muted-foreground">
                          {step.description}
                        </p>
                      </div>
                    </div>
                    {index < steps.length - 1 && (
                      <ChevronRight className="w-5 h-5 text-muted-foreground mx-4" />
                    )}
                  </div>
                )
              })}
            </div>
          </CardContent>
        </Card>

        {/* Progress Summary */}
        {(brand || formData.template || formData.languages.length > 0) && (
          <Card className="mb-6">
            <CardHeader>
              <CardTitle className="text-base">Resum de la selecció</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-wrap gap-4 text-sm">
                {brand && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Marca</Badge>
                    <span>{getSelectedBrand()?.name}</span>
                  </div>
                )}
                {formData.template && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Plantilla</Badge>
                    <span>{getSelectedTemplate()?.name}</span>
                  </div>
                )}
                {formData.languages.length > 0 && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Idiomes</Badge>
                    <span>{formData.languages.join(", ").toUpperCase()}</span>
                  </div>
                )}
                {formData.name && (
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">Nom</Badge>
                    <span>{formData.name}</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Main Content */}
        <Card>
          <CardContent className="p-8">
            {currentStep === 1 && <SelectBrand />}
            {currentStep === 2 && <SelectTemplate />}
            {currentStep === 3 && <SelectLanguagesAndNewsletterName />}
          </CardContent>
        </Card>

        {/* Navigation */}
        <div className="flex justify-between mt-8">
          <Button
            variant="outline"
            onClick={() => setCurrentStep(currentStep - 1)}
            disabled={!canMoveBack}
            className={currentStep === 1 ? 'invisible' : ''}
          >
            Enrere
          </Button>

          <Button
            onClick={() => {
              if (currentStep < 3) {
                setCurrentStep(currentStep + 1)
              } else {
                // Handle form submission here
                console.log('Submit form:', { brand, ...formData })
              }
            }}
            disabled={!canMoveNext}
          >
            {currentStep === 3 ? 'Crear Newsletter' : 'Següent'}
          </Button>
        </div>
      </div>
    </div>
  )
}

export default Page