"use client"

import * as React from "react"
import {
  ColumnDef,
  ColumnFiltersState,
  SortingState,
  VisibilityState,
  flexRender,
  getCoreRowModel,
  getFilteredRowModel,
  getPaginationRowModel,
  getSortedRowModel,
  useReactTable,
} from "@tanstack/react-table"
import { ArrowUpDown, ChevronDown, MoreHorizontal, Plus, Pencil, Trash2, X, Eye, ChevronLeft, ChevronRight, ChevronsLeft, ChevronsRight } from "lucide-react"

import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuCheckboxItem,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Input } from "@/components/ui/input"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Badge } from "@/components/ui/badge"
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { ScrollArea } from "../ui/scroll-area"
import { UpdateConfirmationDialog } from "@/components/ui/update-confirmation-dialog"
import { hasChanges, extractRoleData } from "@/lib/change-detection"
import { toast } from "sonner"

interface Permission {
  id: number
  name: string
  codename: string
  content_type: number
}

interface Role {
  id: number
  name: string
  permissions: Permission[]
}

interface PaginationProps {
  page: number
  pageSize: number
  totalCount: number
  hasNext: boolean
  hasPrevious: boolean
  onPageChange: (page: number) => void
  onPageSizeChange: (pageSize: number) => void
}

interface RoleManagementProps {
  roles: Role[]
  permissions: Permission[]
  onCreateRole: (role: { name: string; permissions: number[] }) => void
  onUpdateRole: (id: number, role: { name: string; permissions: number[] }) => void
  onDeleteRole: (id: number) => void
  pagination?: PaginationProps
  searchQuery?: string
  onSearchChange?: (query: string) => void
}



export function RoleManagement({ roles, permissions, onCreateRole, onUpdateRole, onDeleteRole, pagination, searchQuery, onSearchChange }: RoleManagementProps) {
  const [sorting, setSorting] = React.useState<SortingState>([])
  const [columnFilters, setColumnFilters] = React.useState<ColumnFiltersState>([])
  const [columnVisibility, setColumnVisibility] = React.useState<VisibilityState>({})
  const [rowSelection, setRowSelection] = React.useState({})
  const [isCreateDialogOpen, setIsCreateDialogOpen] = React.useState(false)
  const [isEditDialogOpen, setIsEditDialogOpen] = React.useState(false)
  const [isDeleteDialogOpen, setIsDeleteDialogOpen] = React.useState(false)
  const [isViewInfoDialogOpen, setIsViewInfoDialogOpen] = React.useState(false)
  const [selectedRole, setSelectedRole] = React.useState<Role | null>(null)
  const [originalRoleData, setOriginalRoleData] = React.useState<any>(null)
  const [showConfirmDialog, setShowConfirmDialog] = React.useState(false)
  const [formData, setFormData] = React.useState({
    name: "",
    permissions: [] as number[]
  })

  const columns: ColumnDef<Role>[] = [
    {
      accessorKey: "name",
      header: ({ column }) => {
        return (
          <Button
            variant="ghost"
            onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}
          >
            Name
            <ArrowUpDown className="ml-2 h-4 w-4" />
          </Button>
        )
      },
      cell: ({ row }) => (
        <div
          className="font-medium pl-4 cursor-pointer hover:text-blue-600 hover:underline"
          onClick={() => {
            setSelectedRole(row.original)
            setIsViewInfoDialogOpen(true)
          }}
        >
          {row.getValue("name")}
        </div>
      ),
    },
    {
      accessorKey: "permissions",
      header: "Permissions",
      cell: ({ row }) => {
        const permissions = row.getValue("permissions") as Permission[]
        return (
          <div className="flex flex-wrap gap-1">
            {permissions.slice(0, 3).map((permission) => (
              <Badge key={permission.id} variant="secondary" className="text-xs">
                {permission.name}
              </Badge>
            ))}
            {permissions.length > 3 && (
              <Tooltip >
                <TooltipTrigger>
                  <Badge variant="outline" className="text-xs">
                    +{permissions.length - 3} more
                  </Badge>

                </TooltipTrigger>
                <TooltipContent className="bg-white border shadow-lg">
                  <ScrollArea>
                    <div className="max-h-60 w-48 p-2">
                      {permissions
                        .slice(3).map((permission) => (
                          <div key={permission.id} className="text-sm text-foreground border-b last:border-0 border-gray-200 py-1">
                            {permission.name}
                          </div>
                        ))}
                    </div>
                  </ScrollArea>
                </TooltipContent>
              </Tooltip>
            )}
          </div>
        )
      },
    },
    {
      accessorKey: "actions",
      header: "Accions",
      cell: ({ row }) => {
        const role = row.original

        return (
          <div className="flex gap-2">
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                setSelectedRole(role)
                setIsEditDialogOpen(true)
              }}
            >
              <Pencil className="h-4 w-4" />
            </Button>
            <Button
              variant="ghost"
              size="icon"
              onClick={() => {
                setSelectedRole(role)
                setIsDeleteDialogOpen(true)
              }}
            >
              <Trash2 className="h-4 w-4 text-red-600" />
            </Button>
          </div>
        )
      }
    },
  ]

  React.useEffect(() => {
    if (!isEditDialogOpen || !selectedRole) {
      setFormData({
        name: "",
        permissions: []
      })
      return
    }
    
    const roleData = {
      name: selectedRole.name,
      permissions: selectedRole.permissions.map(p => p.id)
    }
    setFormData(roleData)
    setOriginalRoleData(extractRoleData({
      name: selectedRole.name,
      permissions: selectedRole.permissions.map(p => p.id)
    }))
  }, [isEditDialogOpen, selectedRole])

  React.useEffect(() => {
    if (isCreateDialogOpen) return;
    setFormData({
      name: "",
      permissions: []
    })

  }, [isCreateDialogOpen])

  const table = useReactTable({
    data: roles,
    columns: columns,
    onSortingChange: setSorting,
    onColumnFiltersChange: setColumnFilters,
    getCoreRowModel: getCoreRowModel(),
    ...(pagination ? {} : { getPaginationRowModel: getPaginationRowModel() }),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    onColumnVisibilityChange: setColumnVisibility,
    onRowSelectionChange: setRowSelection,
    ...(pagination ? {
      manualPagination: true,
      pageCount: Math.ceil(pagination.totalCount / pagination.pageSize),
    } : {}),
    state: {
      sorting,
      columnFilters,
      columnVisibility,
      rowSelection,
      ...(pagination ? {
        pagination: {
          pageIndex: pagination.page - 1, // TanStack uses 0-based indexing
          pageSize: pagination.pageSize,
        },
      } : {}),
    },
  })

  const handleCreateRole = () => {
    onCreateRole({
      name: formData.name,
      permissions: formData.permissions
    })
    setFormData({ name: "", permissions: [] })
    setIsCreateDialogOpen(false)
  }

  const handleUpdateRole = () => {
    if (!selectedRole) return

    // Check if there are any changes
    if (!hasChanges(originalRoleData, formData)) {
      toast.info('No s\'han detectat canvis')
      return
    }

    // Show confirmation dialog
    setShowConfirmDialog(true)
  }

  const handleConfirmUpdate = () => {
    if (selectedRole) {
      onUpdateRole(selectedRole.id, {
        name: formData.name,
        permissions: formData.permissions
      })
      setIsEditDialogOpen(false)
      setSelectedRole(null)
      setShowConfirmDialog(false)
    }
  }

  const handleDeleteRole = () => {
    if (selectedRole) {
      onDeleteRole(selectedRole.id)
      setIsDeleteDialogOpen(false)
      setSelectedRole(null)
    }
  }

  // Multi-select permission component
  const PermissionMultiSelect = ({
    selectedPermissions,
    onPermissionChange
  }: {
    selectedPermissions: number[],
    onPermissionChange: (permissions: number[]) => void
  }) => {
    const [searchTerm, setSearchTerm] = React.useState("")
    const [isOpen, setIsOpen] = React.useState(false)

    const filteredPermissions = permissions.filter(permission =>
      permission.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      permission.codename.toLowerCase().includes(searchTerm.toLowerCase())
    )

    const togglePermission = (permissionId: number) => {
      const newPermissions = selectedPermissions.includes(permissionId)
        ? selectedPermissions.filter(id => id !== permissionId)
        : [...selectedPermissions, permissionId]
      onPermissionChange(newPermissions)
    }

    return (
      <div className="space-y-2">
        <DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
          <DropdownMenuTrigger asChild>
            <Button
              variant="outline"
              className="w-full justify-between"
              role="combobox"
              aria-expanded={isOpen}
            >
              <span className="truncate">
                {selectedPermissions.length === 0
                  ? "Seleccionar permisos..."
                  : `${selectedPermissions.length} permiso${selectedPermissions.length !== 1 ? 's' : ''} seleccionado${selectedPermissions.length !== 1 ? 's' : ''}`
                }
              </span>
              <ChevronDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent className="w-80 p-0">
            <div className="p-2">
              <Input
                placeholder="Buscar permisos..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="h-8"
              />
            </div>
            <DropdownMenuSeparator />
            <div className="max-h-60 overflow-y-auto">
              {filteredPermissions.length === 0 ? (
                <div className="p-2 text-sm text-muted-foreground">
                  No se encontraron permisos
                </div>
              ) : (
                filteredPermissions.map((permission) => (
                  <DropdownMenuCheckboxItem
                    key={permission.id}
                    checked={selectedPermissions.includes(permission.id)}
                    onCheckedChange={() => togglePermission(permission.id)}
                    className="cursor-pointer"
                  >
                    <div className="flex flex-col">
                      <span className="font-medium">{permission.name}</span>
                    </div>
                  </DropdownMenuCheckboxItem>
                ))
              )}
            </div>
          </DropdownMenuContent>
        </DropdownMenu>

        {selectedPermissions.length > 0 && (
          <div className="space-y-1">
            <Label className="text-sm text-muted-foreground">Permisos seleccionados:</Label>
            <div className="flex flex-wrap gap-1 max-h-20 overflow-y-auto">
              {selectedPermissions.map((permissionId) => {
                const permission = permissions.find(p => p.id === permissionId)
                return permission ? (
                  <Badge
                    key={permissionId}
                    variant="secondary"
                    className="text-xs flex items-center gap-1"
                  >
                    {permission.name}
                    <X
                      className="h-3 w-3 cursor-pointer hover:text-destructive"
                      onClick={() => togglePermission(permissionId)}
                    />
                  </Badge>
                ) : null
              })}
            </div>
          </div>
        )}
      </div>
    )
  }

  return (
    <div className="w-full">
      <div className="flex items-center justify-between py-4">
        <Input
          placeholder="Cercar rols..."
          value={searchQuery || ((table.getColumn("name")?.getFilterValue() as string) ?? "")}
          onChange={(event) => {
            if (onSearchChange) {
              onSearchChange(event.target.value)
            } else {
              table.getColumn("name")?.setFilterValue(event.target.value)
            }
          }}
          className="max-w-sm"
        />
        <div className="flex items-center space-x-2">
          {/* <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline">
                Columnas <ChevronDown className="ml-2 h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              {table
                .getAllColumns()
                .filter((column) => column.getCanHide())
                .map((column) => {
                  return (
                    <DropdownMenuCheckboxItem
                      key={column.id}
                      className="capitalize"
                      checked={column.getIsVisible()}
                      onCheckedChange={(value) =>
                        column.toggleVisibility(!!value)
                      }
                    >
                      {column.id}
                    </DropdownMenuCheckboxItem>
                  )
                })}
            </DropdownMenuContent>
          </DropdownMenu> */}

          <Dialog open={isCreateDialogOpen} onOpenChange={setIsCreateDialogOpen}>
            <DialogTrigger asChild>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                Añadir Rol
              </Button>
            </DialogTrigger>
            <DialogContent className="sm:max-w-[525px]">
              <DialogHeader>
                <DialogTitle>Crear Rol</DialogTitle>
                <DialogDescription>
                  Añadir un nuevo rol al sistema. Haz clic en guardar cuando hayas terminado.
                </DialogDescription>
              </DialogHeader>
              <div className="grid gap-4 py-4">
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="name-role" className="text-right">
                    Nombre
                  </Label>
                  <Input
                    id="role"
                    value={formData.name}
                    onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                    className="col-span-3"
                  />
                </div>

                <div className="grid grid-cols-4 items-start gap-4">
                  <Label className="text-right">
                    Permisos
                  </Label>
                  <div className="col-span-3">
                    <PermissionMultiSelect
                      selectedPermissions={formData.permissions}
                      onPermissionChange={(permissions) => setFormData({ ...formData, permissions })}
                    />
                  </div>
                </div>
              </div>
              <DialogFooter>
                <Button type="submit" onClick={handleCreateRole}>
                  Crear Rol
                </Button>
              </DialogFooter>
            </DialogContent>
          </Dialog>
        </div>
      </div>

      {/* Edit Dialog */}
      <Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
        <DialogContent className="sm:max-w-[525px]">
          <DialogHeader>
            <DialogTitle>Editar Rol</DialogTitle>
            <DialogDescription>
              Realiza cambios en el rol aquí. Haz clic en guardar cuando hayas terminado.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label htmlFor="edit-name" className="text-right">
                Nombre
              </Label>
              <Input
                id="edit-name"
                value={formData.name}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
                className="col-span-3"
              />
            </div>

            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right">
                Permisos
              </Label>
              <div className="col-span-3">
                <PermissionMultiSelect
                  selectedPermissions={formData.permissions}
                  onPermissionChange={(permissions) => setFormData({ ...formData, permissions })}
                />
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button type="submit" onClick={handleUpdateRole}>
              Guardar Cambios
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Update Confirmation Dialog */}
      <UpdateConfirmationDialog
        open={showConfirmDialog}
        onOpenChange={setShowConfirmDialog}
        onConfirm={handleConfirmUpdate}
        title="Confirmar Actualització de Rol"
        description="Estàs segur que vols actualitzar aquest rol? Aquesta acció desarà tots els teus canvis."
        confirmText="Actualitzar Rol"
        cancelText="Cancel·lar"
      />

      {/* Delete Dialog */}
      <Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Eliminar Rol</DialogTitle>
            <DialogDescription>
              ¿Estás seguro de que deseas eliminar el rol &quot;{selectedRole?.name}&quot;? Esta acción no se puede deshacer.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsDeleteDialogOpen(false)}>
              Cancelar
            </Button>
            <Button variant="destructive" onClick={handleDeleteRole}>
              Eliminar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* View Info Dialog */}
      <Dialog open={isViewInfoDialogOpen} onOpenChange={setIsViewInfoDialogOpen}>
        <DialogContent className="sm:max-w-[600px]">
          <DialogHeader>
            <DialogTitle>Información del Rol</DialogTitle>
            <DialogDescription>
              Detalles completos del rol seleccionado.
            </DialogDescription>
          </DialogHeader>
          <div className="grid gap-4 py-4">
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-semibold">
                ID:
              </Label>
              <div className="col-span-3">
                {selectedRole?.id}
              </div>
            </div>
            <div className="grid grid-cols-4 items-center gap-4">
              <Label className="text-right font-semibold">
                Nombre:
              </Label>
              <div className="col-span-3">
                {selectedRole?.name}
              </div>
            </div>
            <div className="grid grid-cols-4 items-start gap-4">
              <Label className="text-right font-semibold">
                Permisos:
              </Label>
              <div className="col-span-3">
                {selectedRole?.permissions && selectedRole.permissions.length > 0 ? (
                  <div className="space-y-2">
                    <div className="text-sm text-muted-foreground">
                      Total: {selectedRole.permissions.length} permisos
                    </div>
                    <div className="flex flex-wrap gap-1 max-h-40 overflow-y-auto">
                      {selectedRole.permissions.map((permission) => (
                        <Badge key={permission.id} variant="secondary" className="text-xs">
                          {permission.name}
                        </Badge>
                      ))}
                    </div>
                  </div>
                ) : (
                  <div className="text-sm text-muted-foreground">
                    No hay permisos asignados
                  </div>
                )}
              </div>
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setIsViewInfoDialogOpen(false)}>
              Cerrar
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => {
                  return (
                    <TableHead key={header.id}>
                      {header.isPlaceholder
                        ? null
                        : flexRender(
                          header.column.columnDef.header,
                          header.getContext()
                        )}
                    </TableHead>
                  )
                })}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && "selected"}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {flexRender(
                        cell.column.columnDef.cell,
                        cell.getContext()
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={columns.length}
                  className="h-24 text-center"
                >
                  No hay resultados.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
      {/* Pagination Controls */}
      {pagination && (
        <div className="flex items-center justify-between space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            Mostrant {((pagination.page - 1) * pagination.pageSize) + 1} a{" "}
            {Math.min(pagination.page * pagination.pageSize, pagination.totalCount)} de{" "}
            {pagination.totalCount} entrades
          </div>
          <div className="flex items-center space-x-6 lg:space-x-8">
            <div className="flex items-center space-x-2">
              <p className="text-sm font-medium">Files per pàgina</p>
              <select
                value={pagination.pageSize}
                onChange={(e) => {
                  pagination.onPageSizeChange(Number(e.target.value))
                  pagination.onPageChange(1) // Reset to first page when changing page size
                }}
                className="h-8 w-[70px] rounded border border-input bg-background px-3 py-1 text-sm ring-offset-background focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2"
              >
                {[10, 20, 30, 40, 50].map((pageSize) => (
                  <option key={pageSize} value={pageSize}>
                    {pageSize}
                  </option>
                ))}
              </select>
            </div>
            <div className="flex w-[100px] items-center justify-center text-sm font-medium">
              Pàgina {pagination.page} de{" "}
              {Math.ceil(pagination.totalCount / pagination.pageSize)}
            </div>
            <div className="flex items-center space-x-2">
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la primera pàgina</span>
                <ChevronsLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page - 1)}
                disabled={!pagination.hasPrevious}
              >
                <span className="sr-only">Anar a la pàgina anterior</span>
                <ChevronLeft className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="h-8 w-8 p-0"
                onClick={() => pagination.onPageChange(pagination.page + 1)}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a la pàgina següent</span>
                <ChevronRight className="h-4 w-4" />
              </Button>
              <Button
                variant="outline"
                className="hidden h-8 w-8 p-0 lg:flex"
                onClick={() => pagination.onPageChange(Math.ceil(pagination.totalCount / pagination.pageSize))}
                disabled={!pagination.hasNext}
              >
                <span className="sr-only">Anar a l'última pàgina</span>
                <ChevronsRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      )}

      {/* Fallback pagination for non-paginated mode */}
      {!pagination && (
        <div className="flex items-center justify-end space-x-2 py-4">
          <div className="flex-1 text-sm text-muted-foreground">
            {table.getFilteredSelectedRowModel().rows.length} of{" "}
            {table.getFilteredRowModel().rows.length} row(s) selected.
          </div>
          <div className="space-x-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.previousPage()}
              disabled={!table.getCanPreviousPage()}
            >
              Anterior
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => table.nextPage()}
              disabled={!table.getCanNextPage()}
            >
              Siguiente
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
